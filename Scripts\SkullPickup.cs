using UnityEngine;

namespace HELLSTRIKE
{
    public enum SkullType
    {
        Blue,
        Red
    }

    public class SkullPickup : MonoBehaviour
    {
        [Header("Skull Settings")]
        [SerializeField] private SkullType skullType = SkullType.Blue;
        [SerializeField] private bool oneTimePickup = true;
        [SerializeField] private float pickupRange = 3f;
        [SerializeField] private LayerMask playerLayer = -1;
        [SerializeField] private bool autoFindPlayer = true;
        [SerializeField] private bool debugPickup = true;

        [Header("Visual Effects")]
        [SerializeField] private GameObject pickupEffect;
        [SerializeField] private float effectLifetime = 2f;
        [SerializeField] private bool rotatePickup = true;
        [SerializeField] private float rotationSpeed = 50f;
        [SerializeField] private bool bobPickup = true;
        [SerializeField] private float bobHeight = 0.5f;
        [SerializeField] private float bobSpeed = 2f;

        [Header("Audio")]
        [SerializeField] private AudioClip pickupSound;
        [Range(0f, 1f)] [SerializeField] private float pickupVolume = 0.7f;

        [Header("UI")]
        [SerializeField] private string pickupMessage = "Skull Acquired!";
        [SerializeField] private float messageDisplayTime = 3f;
        
        // Private variables
        private Transform player;
        private SkullPuzzleManager puzzleManager;
        private AudioSource audioSource;
        private bool hasBeenPickedUp = false;
        private Vector3 originalPosition;
        private float bobTimer = 0f;
        
        void Start()
        {
            // Store original position for bobbing
            originalPosition = transform.position;
            
            // Auto-find player if enabled
            if (autoFindPlayer && player == null)
            {
                GameObject playerObj = GameObject.FindGameObjectWithTag("Player");
                if (playerObj == null)
                {
                    // Try to find by name if tag doesn't work
                    playerObj = GameObject.Find("Player");
                    if (playerObj == null)
                    {
                        // Try to find first person controller
                        var fpsController = FindFirstObjectByType<CharacterController>();
                        if (fpsController != null)
                        {
                            playerObj = fpsController.gameObject;
                        }
                    }
                }
                
                if (playerObj != null)
                {
                    player = playerObj.transform;
                }
            }
            
            // Find puzzle manager
            puzzleManager = FindFirstObjectByType<SkullPuzzleManager>();
            if (puzzleManager == null && debugPickup)
            {
                Debug.LogWarning("SkullPickup: SkullPuzzleManager not found in scene!");
            }
            
            // Setup audio source
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
            
            if (debugPickup)
            {
                Debug.Log($"SkullPickup: Initialized {skullType} skull at {transform.position}");
            }
        }
        
        void Update()
        {
            if (hasBeenPickedUp) return;
            
            // Handle visual effects
            HandleVisualEffects();
            
            // Check for player proximity
            CheckForPlayer();
        }
        
        private void HandleVisualEffects()
        {
            // Rotation effect
            if (rotatePickup)
            {
                transform.Rotate(Vector3.up, rotationSpeed * Time.deltaTime);
            }
            
            // Bobbing effect
            if (bobPickup)
            {
                bobTimer += Time.deltaTime * bobSpeed;
                float bobOffset = Mathf.Sin(bobTimer) * bobHeight;
                transform.position = originalPosition + Vector3.up * bobOffset;
            }
        }
        
        private void CheckForPlayer()
        {
            if (player == null) return;
            
            float distanceToPlayer = Vector3.Distance(transform.position, player.position);
            
            if (distanceToPlayer <= pickupRange)
            {
                // Check if player is on the correct layer
                if (((1 << player.gameObject.layer) & playerLayer) != 0)
                {
                    TryPickup();
                }
            }
        }
        
        private void TryPickup()
        {
            if (hasBeenPickedUp) return;

            // Check if puzzle manager exists
            if (puzzleManager == null)
            {
                Debug.LogWarning("SkullPickup: Cannot pickup skull - no SkullPuzzleManager found!");
                return;
            }

            // Check if player already has a skull
            if (puzzleManager.PlayerHasSkull())
            {
                if (debugPickup)
                {
                    Debug.Log("SkullPickup: Player already has a skull!");
                }
                return;
            }

            // Perform pickup
            PerformPickup();
        }
        
        private void PerformPickup()
        {
            hasBeenPickedUp = true;

            // Give skull to player
            puzzleManager.PickupSkull(skullType);

            // Play pickup sound
            if (pickupSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(pickupSound, pickupVolume);
            }

            // Spawn pickup effect
            if (pickupEffect != null)
            {
                GameObject effect = Instantiate(pickupEffect, transform.position, transform.rotation);
                Destroy(effect, effectLifetime);
            }

            // Display pickup message (you can extend this to show UI message)
            if (debugPickup)
            {
                Debug.Log($"SkullPickup: {pickupMessage}");
            }

            // Destroy pickup object if one-time pickup
            if (oneTimePickup)
            {
                // Delay destruction slightly to allow sound to play
                Destroy(gameObject, 0.1f);
            }
            else
            {
                // Hide the pickup for a while if not one-time
                gameObject.SetActive(false);
                Invoke(nameof(ReactivatePickup), 10f); // Reactivate after 10 seconds
            }

            if (debugPickup)
            {
                Debug.Log($"SkullPickup: {skullType} skull successfully picked up!");
            }
        }
        
        private void ReactivatePickup()
        {
            if (!oneTimePickup)
            {
                hasBeenPickedUp = false;
                gameObject.SetActive(true);
            }
        }
        
        // Trigger-based pickup (alternative to distance-based)
        void OnTriggerEnter(Collider other)
        {
            if (hasBeenPickedUp) return;
            
            // Check if it's the player
            if (((1 << other.gameObject.layer) & playerLayer) != 0 || 
                other.CompareTag("Player") || 
                other.GetComponent<CharacterController>() != null)
            {
                TryPickup();
            }
        }
        
        // Visual feedback in editor
        void OnDrawGizmosSelected()
        {
            // Draw pickup range
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(transform.position, pickupRange);
            
            // Draw pickup effect area
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireCube(transform.position, Vector3.one * 0.5f);
        }
        
        // Public methods for external control
        public void SetPlayer(Transform playerTransform)
        {
            player = playerTransform;
        }
        
        public void SetPuzzleManager(SkullPuzzleManager manager)
        {
            puzzleManager = manager;
        }
        
        public void ForcePickup()
        {
            if (!hasBeenPickedUp)
            {
                PerformPickup();
            }
        }
        
        public bool IsPickedUp()
        {
            return hasBeenPickedUp;
        }
        
        public void ResetPickup()
        {
            hasBeenPickedUp = false;
            gameObject.SetActive(true);
        }
        
        // Properties
        public float PickupRange => pickupRange;
        public string PickupMessage => pickupMessage;
        public SkullType SkullType => skullType;
    }
}
