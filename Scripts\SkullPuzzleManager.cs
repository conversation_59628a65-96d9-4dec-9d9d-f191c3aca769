using UnityEngine;
using UnityEngine.Events;
using System.Collections.Generic;

namespace HELLSTRIKE
{
    public class SkullPuzzleManager : MonoBehaviour
    {
        [Header("Puzzle Settings")]
        [SerializeField] private bool debugPuzzle = true;
        [SerializeField] private bool autoFindPedestals = true;
        [SerializeField] private List<SkullPedestal> pedestals = new List<SkullPedestal>();
        
        [Header("Door/Animation")]
        [SerializeField] private GameObject doorToOpen; // The door GameObject to activate/deactivate
        [SerializeField] private Animator doorAnimator; // Animator for door opening animation
        [SerializeField] private string doorOpenTrigger = "Open"; // Animation trigger name
        [SerializeField] private bool deactivateDoorOnOpen = false; // Whether to deactivate door GameObject (DISABLED by default - use animation instead)
        
        [Header("Audio")]
        [SerializeField] private AudioClip puzzleCompleteSound;
        [SerializeField] private AudioClip puzzleResetSound;
        [Range(0f, 1f)] [SerializeField] private float audioVolume = 0.8f;
        
        [Header("Effects")]
        [SerializeField] private GameObject puzzleCompleteEffect;
        [SerializeField] private float effectLifetime = 3f;
        
        [Header("Events")]
        public UnityEvent OnPuzzleCompleted;
        public UnityEvent OnPuzzleReset;
        
        // Private variables
        private bool playerHasSkull = false;
        private SkullType playerSkullType = SkullType.Blue;
        private bool puzzleCompleted = false;
        private bool doorOpened = false; // Track if door has been opened
        private AudioSource audioSource;
        private SkullInventoryUI inventoryUI;
        
        // Track which pedestals have skulls
        private Dictionary<SkullType, bool> pedestalStates = new Dictionary<SkullType, bool>
        {
            { SkullType.Blue, false },
            { SkullType.Red, false }
        };
        
        void Start()
        {
            // Setup audio source
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
            
            // Find inventory UI
            inventoryUI = FindFirstObjectByType<SkullInventoryUI>();
            if (inventoryUI == null && debugPuzzle)
            {
                Debug.LogWarning("SkullPuzzleManager: SkullInventoryUI not found in scene!");
            }
            
            // Auto-find pedestals if enabled
            if (autoFindPedestals)
            {
                SkullPedestal[] foundPedestals = FindObjectsByType<SkullPedestal>(FindObjectsSortMode.None);
                foreach (var pedestal in foundPedestals)
                {
                    if (!pedestals.Contains(pedestal))
                    {
                        pedestals.Add(pedestal);
                        pedestal.SetPuzzleManager(this);
                    }
                }
            }
            
            // Set puzzle manager reference for all pedestals
            foreach (var pedestal in pedestals)
            {
                if (pedestal != null)
                {
                    pedestal.SetPuzzleManager(this);
                }
            }
            
            // Auto-find door components if not set
            if (doorToOpen != null && doorAnimator == null)
            {
                doorAnimator = doorToOpen.GetComponent<Animator>();
            }
            
            if (debugPuzzle)
            {
                Debug.Log($"SkullPuzzleManager: Initialized with {pedestals.Count} pedestals");
            }
        }
        
        // Called by SkullPickup when player picks up a skull
        public void PickupSkull(SkullType skullType)
        {
            if (playerHasSkull)
            {
                if (debugPuzzle)
                {
                    Debug.Log("SkullPuzzleManager: Player already has a skull!");
                }
                return;
            }
            
            playerHasSkull = true;
            playerSkullType = skullType;
            
            // Update UI
            if (inventoryUI != null)
            {
                inventoryUI.ShowSkull(skullType);
            }
            
            if (debugPuzzle)
            {
                Debug.Log($"SkullPuzzleManager: Player picked up {skullType} skull");
            }
        }
        
        // Called by SkullPedestal when player places a skull
        public void PlaceSkull()
        {
            if (!playerHasSkull)
            {
                if (debugPuzzle)
                {
                    Debug.Log("SkullPuzzleManager: Player doesn't have a skull to place!");
                }
                return;
            }
            
            playerHasSkull = false;
            
            // Update UI
            if (inventoryUI != null)
            {
                inventoryUI.HideSkull();
            }
            
            if (debugPuzzle)
            {
                Debug.Log($"SkullPuzzleManager: Player placed {playerSkullType} skull");
            }
        }
        
        // Called by SkullPedestal when a skull is placed on it
        public void OnSkullPlacedOnPedestal(SkullType skullType)
        {
            pedestalStates[skullType] = true;
            
            if (debugPuzzle)
            {
                Debug.Log($"SkullPuzzleManager: {skullType} skull placed on pedestal");
            }
            
            CheckPuzzleCompletion();
        }
        
        // Called by SkullPedestal when a skull is removed from it
        public void OnSkullRemovedFromPedestal(SkullType skullType)
        {
            pedestalStates[skullType] = false;
            
            if (debugPuzzle)
            {
                Debug.Log($"SkullPuzzleManager: {skullType} skull removed from pedestal");
            }
            
            // Reset puzzle if it was completed
            if (puzzleCompleted)
            {
                ResetPuzzle();
            }
        }
        
        private void CheckPuzzleCompletion()
        {
            // Check if all required skulls are placed
            bool allSkullsPlaced = true;
            foreach (var state in pedestalStates.Values)
            {
                if (!state)
                {
                    allSkullsPlaced = false;
                    break;
                }
            }
            
            if (allSkullsPlaced && !puzzleCompleted)
            {
                CompletePuzzle();
            }
        }
        
        private void CompletePuzzle()
        {
            if (puzzleCompleted) return; // Prevent multiple completions

            puzzleCompleted = true;

            // Open the door
            OpenDoor();

            // Play completion effects
            PlayCompletionEffects();

            // Trigger events
            OnPuzzleCompleted?.Invoke();

            if (debugPuzzle)
            {
                Debug.Log("SkullPuzzleManager: Puzzle completed! Door opened!");
            }
        }
        
        private void ResetPuzzle()
        {
            puzzleCompleted = false;
            doorOpened = false; // Reset door state

            // Close the door (if you want this behavior)
            // CloseDoor();

            // Play reset effects
            if (puzzleResetSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(puzzleResetSound, audioVolume);
            }

            // Trigger events
            OnPuzzleReset?.Invoke();

            if (debugPuzzle)
            {
                Debug.Log("SkullPuzzleManager: Puzzle reset!");
            }
        }
        
        private void OpenDoor()
        {
            if (doorToOpen == null || doorOpened) return; // Prevent opening door multiple times

            doorOpened = true;

            if (debugPuzzle)
            {
                Debug.Log($"SkullPuzzleManager: Opening door...");
                Debug.Log($"SkullPuzzleManager: Door GameObject: {doorToOpen.name}");
                Debug.Log($"SkullPuzzleManager: Door Animator assigned: {doorAnimator != null}");
                Debug.Log($"SkullPuzzleManager: Door Open Trigger: '{doorOpenTrigger}'");
                Debug.Log($"SkullPuzzleManager: Deactivate Door On Open: {deactivateDoorOnOpen}");
            }

            // Play animation if animator is assigned
            if (doorAnimator != null && !string.IsNullOrEmpty(doorOpenTrigger))
            {
                doorAnimator.SetTrigger(doorOpenTrigger);

                if (debugPuzzle)
                {
                    Debug.Log($"SkullPuzzleManager: Successfully triggered door animation '{doorOpenTrigger}'");
                }
            }
            else
            {
                if (debugPuzzle)
                {
                    Debug.LogWarning($"SkullPuzzleManager: Cannot play door animation! Animator: {doorAnimator != null}, Trigger: '{doorOpenTrigger}'");
                }
            }

            // Only deactivate door if specifically requested (now disabled by default)
            if (deactivateDoorOnOpen)
            {
                if (debugPuzzle)
                {
                    Debug.Log("SkullPuzzleManager: Deactivating door GameObject (this will stop the animation!)");
                }
                doorToOpen.SetActive(false);
            }
            else
            {
                if (debugPuzzle)
                {
                    Debug.Log("SkullPuzzleManager: Door GameObject remains active to play animation");
                }
            }
        }
        
        private void PlayCompletionEffects()
        {
            // Play sound
            if (puzzleCompleteSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(puzzleCompleteSound, audioVolume);
            }
            
            // Spawn effect
            if (puzzleCompleteEffect != null)
            {
                Vector3 effectPosition = transform.position;
                
                // Try to position effect at the center of all pedestals
                if (pedestals.Count > 0)
                {
                    Vector3 centerPosition = Vector3.zero;
                    int validPedestals = 0;
                    
                    foreach (var pedestal in pedestals)
                    {
                        if (pedestal != null)
                        {
                            centerPosition += pedestal.transform.position;
                            validPedestals++;
                        }
                    }
                    
                    if (validPedestals > 0)
                    {
                        effectPosition = centerPosition / validPedestals;
                    }
                }
                
                GameObject effect = Instantiate(puzzleCompleteEffect, effectPosition, Quaternion.identity);
                Destroy(effect, effectLifetime);
            }
        }
        
        // Public methods for external access
        public bool PlayerHasSkull() => playerHasSkull;
        public SkullType GetPlayerSkullType() => playerSkullType;
        public bool IsPuzzleCompleted() => puzzleCompleted;
        
        public void SetInventoryUI(SkullInventoryUI ui)
        {
            inventoryUI = ui;
        }
        
        public void AddPedestal(SkullPedestal pedestal)
        {
            if (pedestal != null && !pedestals.Contains(pedestal))
            {
                pedestals.Add(pedestal);
                pedestal.SetPuzzleManager(this);
            }
        }
        
        public void RemovePedestal(SkullPedestal pedestal)
        {
            if (pedestals.Contains(pedestal))
            {
                pedestals.Remove(pedestal);
            }
        }
        
        // Force reset the entire puzzle
        public void ForceResetPuzzle()
        {
            // Reset player inventory
            playerHasSkull = false;
            if (inventoryUI != null)
            {
                inventoryUI.HideSkull();
            }
            
            // Reset all pedestal states
            foreach (var key in new List<SkullType>(pedestalStates.Keys))
            {
                pedestalStates[key] = false;
            }
            
            // Force remove skulls from pedestals
            foreach (var pedestal in pedestals)
            {
                if (pedestal != null)
                {
                    pedestal.ForceRemoveSkull();
                }
            }
            
            puzzleCompleted = false;
            doorOpened = false; // Reset door state

            if (debugPuzzle)
            {
                Debug.Log("SkullPuzzleManager: Puzzle force reset!");
            }
        }
    }
}
